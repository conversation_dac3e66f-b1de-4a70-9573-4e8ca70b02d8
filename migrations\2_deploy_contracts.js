/*const SimpleStorage = artifacts.require("SimpleStorage");

module.exports = function(deployer) {
  deployer.deploy(SimpleStorage);
};*/

const SimpleStorage = artifacts.require("SimpleStorage");

module.exports = async function(deployer, network, accounts) {
  await deployer.deploy(SimpleStorage);
  const instance = await SimpleStorage.deployed();
  await instance.set(450);
  console.log("Initial value set to 122");
};
{"contractName": "SimpleStorage", "abi": [{"inputs": [{"internalType": "uint256", "name": "x", "type": "uint256"}], "name": "set", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "get", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "constant": true}], "metadata": "{\"compiler\":{\"version\":\"0.8.19+commit.7dd6d404\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"get\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"x\",\"type\":\"uint256\"}],\"name\":\"set\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"project:/contracts/SimpleStorage.sol\":\"SimpleStorage\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/SimpleStorage.sol\":{\"keccak256\":\"0x9e94e19545de4896aaf1773670586e03cade8d63a3fc3fb93260badb7f2b6574\",\"urls\":[\"bzz-raw://eb2f6420edcee7d76c39fd8b29e6b732bac9c28963f9a4e17d8da100dabe7c8a\",\"dweb:/ipfs/QmSNs8CRYDSrKzwP4Wqu7n85k1i4JCX7gPv7Dp6QyMgqWH\"]}},\"version\":1}", "bytecode": "0x608060405234801561001057600080fd5b50610150806100206000396000f3fe608060405234801561001057600080fd5b50600436106100365760003560e01c806360fe47b11461003b5780636d4ce63c14610057575b600080fd5b610055600480360381019061005091906100c3565b610075565b005b61005f61007f565b60405161006c91906100ff565b60405180910390f35b8060008190555050565b60008054905090565b600080fd5b6000819050919050565b6100a08161008d565b81146100ab57600080fd5b50565b6000813590506100bd81610097565b92915050565b6000602082840312156100d9576100d8610088565b5b60006100e7848285016100ae565b91505092915050565b6100f98161008d565b82525050565b600060208201905061011460008301846100f0565b9291505056fea2646970667358221220dcde797062deb55e2f20a0ee11e2607edbe7293cd0011a4ceaa11d7f8fa9210b64736f6c63430008130033", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106100365760003560e01c806360fe47b11461003b5780636d4ce63c14610057575b600080fd5b610055600480360381019061005091906100c3565b610075565b005b61005f61007f565b60405161006c91906100ff565b60405180910390f35b8060008190555050565b60008054905090565b600080fd5b6000819050919050565b6100a08161008d565b81146100ab57600080fd5b50565b6000813590506100bd81610097565b92915050565b6000602082840312156100d9576100d8610088565b5b60006100e7848285016100ae565b91505092915050565b6100f98161008d565b82525050565b600060208201905061011460008301846100f0565b9291505056fea2646970667358221220dcde797062deb55e2f20a0ee11e2607edbe7293cd0011a4ceaa11d7f8fa9210b64736f6c63430008130033", "immutableReferences": {}, "generatedSources": [], "deployedGeneratedSources": [{"ast": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:1374:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "47:35:1", "statements": [{"nodeType": "YulAssignment", "src": "57:19:1", "value": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "73:2:1", "type": "", "value": "64"}], "functionName": {"name": "mload", "nodeType": "YulIdentifier", "src": "67:5:1"}, "nodeType": "YulFunctionCall", "src": "67:9:1"}, "variableNames": [{"name": "memPtr", "nodeType": "YulIdentifier", "src": "57:6:1"}]}]}, "name": "allocate_unbounded", "nodeType": "YulFunctionDefinition", "returnVariables": [{"name": "memPtr", "nodeType": "YulTypedName", "src": "40:6:1", "type": ""}], "src": "7:75:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "177:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "194:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "197:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "187:6:1"}, "nodeType": "YulFunctionCall", "src": "187:12:1"}, "nodeType": "YulExpressionStatement", "src": "187:12:1"}]}, "name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nodeType": "YulFunctionDefinition", "src": "88:117:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "300:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "317:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "320:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "310:6:1"}, "nodeType": "YulFunctionCall", "src": "310:12:1"}, "nodeType": "YulExpressionStatement", "src": "310:12:1"}]}, "name": "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db", "nodeType": "YulFunctionDefinition", "src": "211:117:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "379:32:1", "statements": [{"nodeType": "YulAssignment", "src": "389:16:1", "value": {"name": "value", "nodeType": "YulIdentifier", "src": "400:5:1"}, "variableNames": [{"name": "cleaned", "nodeType": "YulIdentifier", "src": "389:7:1"}]}]}, "name": "cleanup_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "361:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nodeType": "YulTypedName", "src": "371:7:1", "type": ""}], "src": "334:77:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "460:79:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "517:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "526:1:1", "type": "", "value": "0"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "529:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nodeType": "YulIdentifier", "src": "519:6:1"}, "nodeType": "YulFunctionCall", "src": "519:12:1"}, "nodeType": "YulExpressionStatement", "src": "519:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "483:5:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "508:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nodeType": "YulIdentifier", "src": "490:17:1"}, "nodeType": "YulFunctionCall", "src": "490:24:1"}], "functionName": {"name": "eq", "nodeType": "YulIdentifier", "src": "480:2:1"}, "nodeType": "YulFunctionCall", "src": "480:35:1"}], "functionName": {"name": "iszero", "nodeType": "YulIdentifier", "src": "473:6:1"}, "nodeType": "YulFunctionCall", "src": "473:43:1"}, "nodeType": "YulIf", "src": "470:63:1"}]}, "name": "validator_revert_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "453:5:1", "type": ""}], "src": "417:122:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "597:87:1", "statements": [{"nodeType": "YulAssignment", "src": "607:29:1", "value": {"arguments": [{"name": "offset", "nodeType": "YulIdentifier", "src": "629:6:1"}], "functionName": {"name": "calldataload", "nodeType": "YulIdentifier", "src": "616:12:1"}, "nodeType": "YulFunctionCall", "src": "616:20:1"}, "variableNames": [{"name": "value", "nodeType": "YulIdentifier", "src": "607:5:1"}]}, {"expression": {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "672:5:1"}], "functionName": {"name": "validator_revert_t_uint256", "nodeType": "YulIdentifier", "src": "645:26:1"}, "nodeType": "YulFunctionCall", "src": "645:33:1"}, "nodeType": "YulExpressionStatement", "src": "645:33:1"}]}, "name": "abi_decode_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nodeType": "YulTypedName", "src": "575:6:1", "type": ""}, {"name": "end", "nodeType": "YulTypedName", "src": "583:3:1", "type": ""}], "returnVariables": [{"name": "value", "nodeType": "YulTypedName", "src": "591:5:1", "type": ""}], "src": "545:139:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "756:263:1", "statements": [{"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "802:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nodeType": "YulIdentifier", "src": "804:77:1"}, "nodeType": "YulFunctionCall", "src": "804:79:1"}, "nodeType": "YulExpressionStatement", "src": "804:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nodeType": "YulIdentifier", "src": "777:7:1"}, {"name": "headStart", "nodeType": "YulIdentifier", "src": "786:9:1"}], "functionName": {"name": "sub", "nodeType": "YulIdentifier", "src": "773:3:1"}, "nodeType": "YulFunctionCall", "src": "773:23:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "798:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nodeType": "YulIdentifier", "src": "769:3:1"}, "nodeType": "YulFunctionCall", "src": "769:32:1"}, "nodeType": "YulIf", "src": "766:119:1"}, {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "895:117:1", "statements": [{"nodeType": "YulVariableDeclaration", "src": "910:15:1", "value": {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "924:1:1", "type": "", "value": "0"}, "variables": [{"name": "offset", "nodeType": "YulTypedName", "src": "914:6:1", "type": ""}]}, {"nodeType": "YulAssignment", "src": "939:63:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "974:9:1"}, {"name": "offset", "nodeType": "YulIdentifier", "src": "985:6:1"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "970:3:1"}, "nodeType": "YulFunctionCall", "src": "970:22:1"}, {"name": "dataEnd", "nodeType": "YulIdentifier", "src": "994:7:1"}], "functionName": {"name": "abi_decode_t_uint256", "nodeType": "YulIdentifier", "src": "949:20:1"}, "nodeType": "YulFunctionCall", "src": "949:53:1"}, "variableNames": [{"name": "value0", "nodeType": "YulIdentifier", "src": "939:6:1"}]}]}]}, "name": "abi_decode_tuple_t_uint256", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "726:9:1", "type": ""}, {"name": "dataEnd", "nodeType": "YulTypedName", "src": "737:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nodeType": "YulTypedName", "src": "749:6:1", "type": ""}], "src": "690:329:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1090:53:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nodeType": "YulIdentifier", "src": "1107:3:1"}, {"arguments": [{"name": "value", "nodeType": "YulIdentifier", "src": "1130:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nodeType": "YulIdentifier", "src": "1112:17:1"}, "nodeType": "YulFunctionCall", "src": "1112:24:1"}], "functionName": {"name": "mstore", "nodeType": "YulIdentifier", "src": "1100:6:1"}, "nodeType": "YulFunctionCall", "src": "1100:37:1"}, "nodeType": "YulExpressionStatement", "src": "1100:37:1"}]}, "name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nodeType": "YulTypedName", "src": "1078:5:1", "type": ""}, {"name": "pos", "nodeType": "YulTypedName", "src": "1085:3:1", "type": ""}], "src": "1025:118:1"}, {"body": {"nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1247:124:1", "statements": [{"nodeType": "YulAssignment", "src": "1257:26:1", "value": {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1269:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1280:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1265:3:1"}, "nodeType": "YulFunctionCall", "src": "1265:18:1"}, "variableNames": [{"name": "tail", "nodeType": "YulIdentifier", "src": "1257:4:1"}]}, {"expression": {"arguments": [{"name": "value0", "nodeType": "YulIdentifier", "src": "1337:6:1"}, {"arguments": [{"name": "headStart", "nodeType": "YulIdentifier", "src": "1350:9:1"}, {"kind": "number", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1361:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nodeType": "YulIdentifier", "src": "1346:3:1"}, "nodeType": "YulFunctionCall", "src": "1346:17:1"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nodeType": "YulIdentifier", "src": "1293:43:1"}, "nodeType": "YulFunctionCall", "src": "1293:71:1"}, "nodeType": "YulExpressionStatement", "src": "1293:71:1"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nodeType": "YulTypedName", "src": "1219:9:1", "type": ""}, {"name": "value0", "nodeType": "YulTypedName", "src": "1231:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nodeType": "YulTypedName", "src": "1242:4:1", "type": ""}], "src": "1149:222:1"}]}, "contents": "{\n\n    function allocate_unbounded() -> memPtr {\n        memPtr := mload(64)\n    }\n\n    function revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() {\n        revert(0, 0)\n    }\n\n    function revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db() {\n        revert(0, 0)\n    }\n\n    function cleanup_t_uint256(value) -> cleaned {\n        cleaned := value\n    }\n\n    function validator_revert_t_uint256(value) {\n        if iszero(eq(value, cleanup_t_uint256(value))) { revert(0, 0) }\n    }\n\n    function abi_decode_t_uint256(offset, end) -> value {\n        value := calldataload(offset)\n        validator_revert_t_uint256(value)\n    }\n\n    function abi_decode_tuple_t_uint256(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint256(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function abi_encode_t_uint256_to_t_uint256_fromStack(value, pos) {\n        mstore(pos, cleanup_t_uint256(value))\n    }\n\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value0,  add(headStart, 0))\n\n    }\n\n}\n", "id": 1, "language": "<PERSON>l", "name": "#utility.yul"}], "sourceMap": "27:211:0:-:0;;;;;;;;;;;;;;;;;;;", "deployedSourceMap": "27:211:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;89:62;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;157:79;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;89:62;143:1;130:10;:14;;;;89:62;:::o;157:79::-;193:7;219:10;;212:17;;157:79;:::o;88:117:1:-;197:1;194;187:12;334:77;371:7;400:5;389:16;;334:77;;;:::o;417:122::-;490:24;508:5;490:24;:::i;:::-;483:5;480:35;470:63;;529:1;526;519:12;470:63;417:122;:::o;545:139::-;591:5;629:6;616:20;607:29;;645:33;672:5;645:33;:::i;:::-;545:139;;;;:::o;690:329::-;749:6;798:2;786:9;777:7;773:23;769:32;766:119;;;804:79;;:::i;:::-;766:119;924:1;949:53;994:7;985:6;974:9;970:22;949:53;:::i;:::-;939:63;;895:117;690:329;;;;:::o;1025:118::-;1112:24;1130:5;1112:24;:::i;:::-;1107:3;1100:37;1025:118;;:::o;1149:222::-;1242:4;1280:2;1269:9;1265:18;1257:26;;1293:71;1361:1;1350:9;1346:17;1337:6;1293:71;:::i;:::-;1149:222;;;;:::o", "source": "pragma solidity >=0.8.19;\n\ncontract SimpleStorage {\n    uint256 private storedData;\n\n    function set(uint256 x) public {\n        storedData = x;\n    }\n\n    function get() public view returns (uint256) {\n        return storedData;\n    }\n}\n\n", "sourcePath": "C:\\Users\\<USER>\\projet1\\contracts\\SimpleStorage.sol", "ast": {"absolutePath": "project:/contracts/SimpleStorage.sol", "exportedSymbols": {"SimpleStorage": [22]}, "id": 23, "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", ">=", "0.8", ".19"], "nodeType": "PragmaDirective", "src": "0:25:0"}, {"abstract": false, "baseContracts": [], "canonicalName": "SimpleStorage", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "id": 22, "linearizedBaseContracts": [22], "name": "SimpleStorage", "nameLocation": "36:13:0", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "id": 3, "mutability": "mutable", "name": "storedData", "nameLocation": "72:10:0", "nodeType": "VariableDeclaration", "scope": 22, "src": "56:26:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "56:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "private"}, {"body": {"id": 12, "nodeType": "Block", "src": "120:31:0", "statements": [{"expression": {"id": 10, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 8, "name": "storedData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3, "src": "130:10:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 9, "name": "x", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 5, "src": "143:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "130:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 11, "nodeType": "ExpressionStatement", "src": "130:14:0"}]}, "functionSelector": "60fe47b1", "id": 13, "implemented": true, "kind": "function", "modifiers": [], "name": "set", "nameLocation": "98:3:0", "nodeType": "FunctionDefinition", "parameters": {"id": 6, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 5, "mutability": "mutable", "name": "x", "nameLocation": "110:1:0", "nodeType": "VariableDeclaration", "scope": 13, "src": "102:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "102:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "101:11:0"}, "returnParameters": {"id": 7, "nodeType": "ParameterList", "parameters": [], "src": "120:0:0"}, "scope": 22, "src": "89:62:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 20, "nodeType": "Block", "src": "202:34:0", "statements": [{"expression": {"id": 18, "name": "storedData", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3, "src": "219:10:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 17, "id": 19, "nodeType": "Return", "src": "212:17:0"}]}, "functionSelector": "6d4ce63c", "id": 21, "implemented": true, "kind": "function", "modifiers": [], "name": "get", "nameLocation": "166:3:0", "nodeType": "FunctionDefinition", "parameters": {"id": 14, "nodeType": "ParameterList", "parameters": [], "src": "169:2:0"}, "returnParameters": {"id": 17, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 16, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 21, "src": "193:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 15, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "193:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "192:9:0"}, "scope": 22, "src": "157:79:0", "stateMutability": "view", "virtual": false, "visibility": "public"}], "scope": 23, "src": "27:211:0", "usedErrors": []}], "src": "0:240:0"}, "compiler": {"name": "solc", "version": "0.8.19+commit.7dd6d404.Emscripten.clang"}, "networks": {"5777": {"events": {}, "links": {}, "address": "******************************************", "transactionHash": "0xe87ff5cff082b8b2a4f8b892f705f5c802532b1deb2cd3caad014ab4fc17fff2"}}, "schemaVersion": "3.4.16", "updatedAt": "2025-09-03T13:01:56.908Z", "networkType": "ethereum", "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}
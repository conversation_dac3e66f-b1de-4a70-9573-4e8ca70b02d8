import { useEffect, useState } from 'react';
import Web3 from 'web3';
import SimpleStorageContract from './SimpleStorage.json';

const SimpleStorageApp = () => {
  const [web3, setWeb3] = useState(null);
  const [contract, setContract] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [storedValue, setStoredValue] = useState('');
  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    const init = async () => {
      // Vérifiez si Web3 est injecté par MetaMask
      if (window.ethereum) {
        const web3Instance = new Web3(window.ethereum);
        try {
          // Demandez à l'utilisateur l'autorisation de se connecter
          await window.ethereum.enable();
          setWeb3(web3Instance);

          const accs = await web3Instance.eth.getAccounts();
          setAccounts(accs);

          // Assurez-vous de remplacer l'adresse ci-dessous par l'adresse réelle de votre contrat déployé
          const deployedNetwork = SimpleStorageContract.networks[5777];
          const instance = new web3Instance.eth.Contract(
            SimpleStorageContract.abi,
            deployedNetwork && deployedNetwork.address,
          );
          setContract(instance);
        } catch (error) {
          console.error("Erreur lors de l'initialisation de Web3", error);
        }
      } else {
        console.log('Veuillez installer MetaMask!');
      }
    };
    init();
  }, []);

  const updateStoredValue = async () => {
    if (contract && accounts[0]) {
      await contract.methods.set(inputValue).send({ from: accounts[0] });
      const value = await contract.methods.get().call();
      setStoredValue(value);
      setInputValue('');
    }
  };

  const getStoredValue = async () => {
    if (contract) {
      const value = await contract.methods.get().call();
      setStoredValue(value);
    }
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Simple Storage DApp</h1>
      <div className="mb-4">
        <button
          onClick={getStoredValue}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Get Stored Value
        </button>
        <p className="mt-2">Stored Value: {storedValue}</p>
      </div>
      <div>
        <input
          type="number"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          className="border-2 border-gray-300 p-2 rounded mr-2"
        />
        <button
          onClick={updateStoredValue}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
        >
          Update Value
        </button>
      </div>
    </div>
  );
};

export default SimpleStorageApp;